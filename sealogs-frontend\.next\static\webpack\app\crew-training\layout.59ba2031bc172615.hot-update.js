"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/layout",{

/***/ "(app-pages-browser)/./src/components/ui/tooltip.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/tooltip.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tooltip: function() { return /* binding */ Tooltip; },\n/* harmony export */   TooltipContent: function() { return /* binding */ TooltipContent; },\n/* harmony export */   TooltipProvider: function() { return /* binding */ TooltipProvider; },\n/* harmony export */   TooltipTrigger: function() { return /* binding */ TooltipTrigger; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-tooltip */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-tooltip@1.2_50e390c4dabde08ed3112eb9f58da500/node_modules/@radix-ui/react-tooltip/dist/index.mjs\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/hooks/use-mobile */ \"(app-pages-browser)/./src/components/hooks/use-mobile.tsx\");\n/* __next_internal_client_entry_do_not_use__ Tooltip,TooltipTrigger,TooltipContent,TooltipProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nconst TooltipProvider = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst Tooltip = (param)=>{\n    let { children, mobileClickable = true, open: controlledOpen, onOpenChange: controlledOnOpenChange, ...props } = param;\n    _s();\n    const isMobile = (0,_components_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_3__.useIsMobile)();\n    const [internalOpen, setInternalOpen] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    // Use click behavior on mobile when mobileClickable is true\n    const shouldUseClickBehavior = isMobile && mobileClickable;\n    // Determine if this is controlled or uncontrolled\n    const isControlled = controlledOpen !== undefined;\n    const open = isControlled ? controlledOpen : internalOpen;\n    const setOpen = isControlled ? controlledOnOpenChange || (()=>{}) : setInternalOpen;\n    // Handle click outside to close tooltip on mobile\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        if (!shouldUseClickBehavior || !open) return;\n        const handleClickOutside = (event)=>{\n            const target = event.target;\n            // Check if click is outside tooltip content and trigger\n            if (!target.closest(\"[data-radix-tooltip-content]\") && !target.closest(\"[data-radix-tooltip-trigger]\")) {\n                setOpen(false);\n            }\n        };\n        // Use a small delay to prevent immediate closing\n        const timeoutId = setTimeout(()=>{\n            document.addEventListener(\"click\", handleClickOutside);\n        }, 100);\n        return ()=>{\n            clearTimeout(timeoutId);\n            document.removeEventListener(\"click\", handleClickOutside);\n        };\n    }, [\n        shouldUseClickBehavior,\n        open,\n        setOpen\n    ]);\n    // Always provide consistent controlled behavior\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        open: shouldUseClickBehavior ? open : undefined,\n        onOpenChange: shouldUseClickBehavior ? setOpen : undefined,\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\tooltip.tsx\",\n        lineNumber: 65,\n        columnNumber: 9\n    }, undefined);\n};\n_s(Tooltip, \"a/lss57/OSR5znXO5A4ATVCSPqI=\", false, function() {\n    return [\n        _components_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_3__.useIsMobile\n    ];\n});\n_c = Tooltip;\nconst TooltipTrigger = /*#__PURE__*/ _s1(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c1 = _s1((param, ref)=>{\n    let { mobileClickable = true, onClick, onPointerDown, ...props } = param;\n    _s1();\n    const isMobile = (0,_components_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_3__.useIsMobile)();\n    const shouldUseClickBehavior = isMobile && mobileClickable;\n    const handleClick = (event)=>{\n        if (shouldUseClickBehavior) {\n            // Prevent event bubbling to avoid immediate closing\n            event.stopPropagation();\n        }\n        onClick === null || onClick === void 0 ? void 0 : onClick(event);\n    };\n    const handlePointerDown = (event)=>{\n        if (shouldUseClickBehavior) {\n            // Prevent default pointer behavior on mobile\n            event.preventDefault();\n        }\n        onPointerDown === null || onPointerDown === void 0 ? void 0 : onPointerDown(event);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_4__.Trigger, {\n        ref: ref,\n        onClick: handleClick,\n        onPointerDown: handlePointerDown,\n        \"data-radix-tooltip-trigger\": \"\",\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\tooltip.tsx\",\n        lineNumber: 106,\n        columnNumber: 9\n    }, undefined);\n}, \"zdJ8C3X+YlDYVai5EPOd8CzoqSU=\", false, function() {\n    return [\n        _components_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_3__.useIsMobile\n    ];\n})), \"zdJ8C3X+YlDYVai5EPOd8CzoqSU=\", false, function() {\n    return [\n        _components_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_3__.useIsMobile\n    ];\n});\n_c2 = TooltipTrigger;\nTooltipTrigger.displayName = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_4__.Trigger.displayName;\nconst TooltipContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c3 = (param, ref)=>{\n    let { className, sideOffset = 4, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_4__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_4__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            \"data-radix-tooltip-content\": \"\",\n            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 overflow-hidden px-3 py-1.5  text-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 bg-fire-bush-100 text-fire-bush-600 rounded-md border border-fire-bush-500\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\tooltip.tsx\",\n            lineNumber: 122,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\tooltip.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, undefined);\n});\n_c4 = TooltipContent;\nTooltipContent.displayName = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_4__.Content.displayName;\n\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"Tooltip\");\n$RefreshReg$(_c1, \"TooltipTrigger$React.forwardRef\");\n$RefreshReg$(_c2, \"TooltipTrigger\");\n$RefreshReg$(_c3, \"TooltipContent$React.forwardRef\");\n$RefreshReg$(_c4, \"TooltipContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/tooltip.tsx\n"));

/***/ })

});