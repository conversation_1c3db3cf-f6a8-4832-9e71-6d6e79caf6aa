"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/layout",{

/***/ "(app-pages-browser)/./src/components/ui/tooltip.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/tooltip.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tooltip: function() { return /* binding */ Tooltip; },\n/* harmony export */   TooltipContent: function() { return /* binding */ TooltipContent; },\n/* harmony export */   TooltipProvider: function() { return /* binding */ TooltipProvider; },\n/* harmony export */   TooltipTrigger: function() { return /* binding */ TooltipTrigger; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-tooltip */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-tooltip@1.2_50e390c4dabde08ed3112eb9f58da500/node_modules/@radix-ui/react-tooltip/dist/index.mjs\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/hooks/use-mobile */ \"(app-pages-browser)/./src/components/hooks/use-mobile.tsx\");\n/* __next_internal_client_entry_do_not_use__ Tooltip,TooltipTrigger,TooltipContent,TooltipProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nconst TooltipProvider = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst Tooltip = (param)=>{\n    let { children, mobileClickable = true, open: controlledOpen, onOpenChange: controlledOnOpenChange, ...props } = param;\n    _s();\n    const isMobile = (0,_components_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_3__.useIsMobile)();\n    const [internalOpen, setInternalOpen] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    // Use click behavior on mobile when mobileClickable is true\n    const shouldUseClickBehavior = isMobile && mobileClickable;\n    // Determine if this is controlled or uncontrolled\n    const isControlled = controlledOpen !== undefined;\n    const open = isControlled ? controlledOpen : internalOpen;\n    const setOpen = isControlled ? controlledOnOpenChange || (()=>{}) : setInternalOpen;\n    // Handle click outside to close tooltip on mobile\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        if (!shouldUseClickBehavior || !open) return;\n        const handleClickOutside = (event)=>{\n            const target = event.target;\n            // Check if click is outside tooltip content and trigger\n            if (!target.closest(\"[data-radix-tooltip-content]\") && !target.closest(\"[data-radix-tooltip-trigger]\")) {\n                setOpen(false);\n            }\n        };\n        // Use a small delay to prevent immediate closing\n        const timeoutId = setTimeout(()=>{\n            document.addEventListener(\"click\", handleClickOutside);\n        }, 100);\n        return ()=>{\n            clearTimeout(timeoutId);\n            document.removeEventListener(\"click\", handleClickOutside);\n        };\n    }, [\n        shouldUseClickBehavior,\n        open,\n        setOpen\n    ]);\n    // Always provide consistent controlled behavior\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        open: shouldUseClickBehavior ? open : undefined,\n        onOpenChange: shouldUseClickBehavior ? setOpen : undefined,\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\tooltip.tsx\",\n        lineNumber: 63,\n        columnNumber: 9\n    }, undefined);\n};\n_s(Tooltip, \"a/lss57/OSR5znXO5A4ATVCSPqI=\", false, function() {\n    return [\n        _components_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_3__.useIsMobile\n    ];\n});\n_c = Tooltip;\nconst TooltipTrigger = /*#__PURE__*/ _s1(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c1 = _s1((param, ref)=>{\n    let { mobileClickable = true, onClick, onPointerDown, ...props } = param;\n    _s1();\n    const isMobile = (0,_components_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_3__.useIsMobile)();\n    const shouldUseClickBehavior = isMobile && mobileClickable;\n    const handleClick = (event)=>{\n        if (shouldUseClickBehavior) {\n            // Prevent event bubbling to avoid immediate closing\n            event.stopPropagation();\n        }\n        onClick === null || onClick === void 0 ? void 0 : onClick(event);\n    };\n    const handlePointerDown = (event)=>{\n        if (shouldUseClickBehavior) {\n            // Prevent default pointer behavior on mobile\n            event.preventDefault();\n        }\n        onPointerDown === null || onPointerDown === void 0 ? void 0 : onPointerDown(event);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_4__.Trigger, {\n        ref: ref,\n        onClick: handleClick,\n        onPointerDown: handlePointerDown,\n        \"data-radix-tooltip-trigger\": \"\",\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\tooltip.tsx\",\n        lineNumber: 104,\n        columnNumber: 9\n    }, undefined);\n}, \"zdJ8C3X+YlDYVai5EPOd8CzoqSU=\", false, function() {\n    return [\n        _components_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_3__.useIsMobile\n    ];\n})), \"zdJ8C3X+YlDYVai5EPOd8CzoqSU=\", false, function() {\n    return [\n        _components_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_3__.useIsMobile\n    ];\n});\n_c2 = TooltipTrigger;\nTooltipTrigger.displayName = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_4__.Trigger.displayName;\nconst TooltipContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c3 = (param, ref)=>{\n    let { className, sideOffset = 4, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_4__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_4__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            \"data-radix-tooltip-content\": \"\",\n            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 overflow-hidden px-3 py-1.5  text-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 bg-fire-bush-100 text-fire-bush-600 rounded-md border border-fire-bush-500\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\tooltip.tsx\",\n            lineNumber: 120,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\tooltip.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, undefined);\n});\n_c4 = TooltipContent;\nTooltipContent.displayName = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_4__.Content.displayName;\n\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"Tooltip\");\n$RefreshReg$(_c1, \"TooltipTrigger$React.forwardRef\");\n$RefreshReg$(_c2, \"TooltipTrigger\");\n$RefreshReg$(_c3, \"TooltipContent$React.forwardRef\");\n$RefreshReg$(_c4, \"TooltipContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/tooltip.tsx\n"));

/***/ })

});